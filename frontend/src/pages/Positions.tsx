import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Button,
  Card,
  CardContent,
  Chip,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TableSortLabel,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Snackbar,
  useTheme,
  useMediaQuery,
  Divider,
  Autocomplete,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Update as UpdateIcon,
  Calculate as CalculateIcon,
  ContentCopy as CloneIcon,
} from '@mui/icons-material';

import { PositionService } from '../services/api/positionService';
import { InstrumentService } from '../services/api/instrumentService';
import {
  Position,
  CreatePositionRequest,
  UpdatePositionRequest,
  PositionsUpdateRequest,
  PositionsUpdateResult,
  PnLRecalculationRequest,
  Instrument,
  ApiResponse
} from '../types/api';
import StopLossModeSelector, { StopLossMode } from '../components/StopLossModeSelector';

interface SortConfig {
  field: keyof Position;
  direction: 'asc' | 'desc';
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

interface EditFormData extends CreatePositionRequest {
  closePrice?: number;
  openDate?: string;
  closeDate?: string;
}

// Extended form data interface to support string values during input
interface EditFormInputData {
  symbol: string;
  position: string | number;
  side: 'BUY' | 'SELL';
  tradePrice: string | number;
  initPortfolioNetValue?: string | number;
  riskUnit?: string | number;
  stopPercent?: string | number;
  bbmbAdjPercent?: string | number;
  closePrice?: string | number;
  openDate?: string; // ISO date string (YYYY-MM-DD)
  closeDate?: string; // ISO date string (YYYY-MM-DD)
  aggressiveStopPercent?: string | number;
  conservativeStopPercent?: string | number;
}

interface CalculatedPnL {
  pnlValue: number;
  pnlPercent: number;
}

const Positions: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Available instruments for symbol selection (matching Watch List pattern)
  const [availableInstruments, setAvailableInstruments] = useState<Instrument[]>([]);
  const [instrumentsLoading, setInstrumentsLoading] = useState(false);
  const [instrumentsLoadedOnce, setInstrumentsLoadedOnce] = useState(false);
  const [instrumentsRetryCount, setInstrumentsRetryCount] = useState(0);

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);

  // Sorting
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdDate',
    direction: 'desc'
  });

  // Filtering
  const [statusFilter, setStatusFilter] = useState<'ALL' | 'OPEN' | 'CLOSED'>('ALL');
  const [sideFilter, setSideFilter] = useState<'ALL' | 'BUY' | 'SELL'>('ALL');

  // Dialog states
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);

  // Form states - using input data interface to support string values during typing
  const [formData, setFormData] = useState<EditFormInputData>({
    symbol: '',
    position: '',
    side: 'BUY',
    tradePrice: '',
    initPortfolioNetValue: '',
    riskUnit: '',
    stopPercent: '',
    bbmbAdjPercent: '',
    closePrice: '',
    openDate: '',
    closeDate: '',
    aggressiveStopPercent: '',
    conservativeStopPercent: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  // Calculated P&L state for real-time display
  const [calculatedPnL, setCalculatedPnL] = useState<CalculatedPnL | null>(null);

  // OHLCV Update states
  const [updatingOHLCV, setUpdatingOHLCV] = useState(false);
  const [recalculatingPnL, setRecalculatingPnL] = useState(false);
  const [showUpdateConfirmation, setShowUpdateConfirmation] = useState(false);
  const [showPnLConfirmation, setShowPnLConfirmation] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [updateResult, setUpdateResult] = useState<PositionsUpdateResult | null>(null);
  const [updateStartTime, setUpdateStartTime] = useState<Date | null>(null);

  // Stop-loss mode selection
  const [stopLossMode, setStopLossMode] = useState<StopLossMode>('STANDARD');

  // Snackbar
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Load positions
  const loadPositions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const statusParam = statusFilter === 'ALL' ? undefined : statusFilter;
      const sideParam = sideFilter === 'ALL' ? undefined : sideFilter;

      const response = await PositionService.getPositions(undefined, statusParam, sideParam);

      if (response.success && response.data) {
        setPositions(response.data);
      } else {
        setError(response.message || 'Failed to load positions');
      }
    } catch (err: any) {
      console.error('Error loading positions:', err);
      setError('Failed to load positions: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [statusFilter, sideFilter]);

  // Load available instruments for symbol selection (matching Watch List pattern)
  const loadAvailableInstruments = useCallback(async (forceRefresh: boolean = false, retryAttempt: number = 0) => {
    try {
      setInstrumentsLoading(true);

      // Use maximum allowed page size (1000) to load as many instruments as possible
      const response = await InstrumentService.getInstruments(0, 1000, 'marketCap', 'desc');

      if (response.success && response.data) {
        setAvailableInstruments(response.data.content);
        setInstrumentsLoadedOnce(true);
        setInstrumentsRetryCount(0);

        // Cache the instruments data
        try {
          localStorage.setItem('positions_instruments_cache', JSON.stringify({
            data: response.data.content,
            timestamp: Date.now()
          }));
        } catch (error) {
          console.error('Error caching instruments:', error);
        }

        if (forceRefresh) {
          setSnackbar({
            open: true,
            message: `Refreshed ${response.data.content.length} instruments for symbol selection`,
            severity: 'success',
          });
        }
      } else {
        // Only show error message if this is a forced refresh or we haven't loaded instruments before
        if (forceRefresh || !instrumentsLoadedOnce) {
          const errorMessage = response.message || 'Failed to load instruments for symbol selection';
          setError(errorMessage);
        }

        // Attempt retry for non-forced refresh if we haven't loaded instruments before
        if (!forceRefresh && !instrumentsLoadedOnce && retryAttempt < 2) {
          setInstrumentsRetryCount(retryAttempt + 1);
          setTimeout(() => {
            loadAvailableInstruments(false, retryAttempt + 1);
          }, 1000 * (retryAttempt + 1)); // Exponential backoff
        }
      }
    } catch (err: any) {
      console.error('Error loading instruments:', err);

      // Enhanced error handling for specific API errors
      let errorMessage = 'Failed to load instruments for symbol selection';
      if (err.message) {
        if (err.message.includes('Page size must be between')) {
          errorMessage = 'Invalid page size parameter. Please contact support.';
        } else if (err.message.includes('400')) {
          errorMessage = 'Invalid request parameters. Please try refreshing the page.';
        } else if (err.message.includes('500')) {
          errorMessage = 'Server error. Please try again later.';
        } else {
          errorMessage = err.message;
        }
      }

      // Only show error message if this is a forced refresh or we haven't loaded instruments before
      if (forceRefresh || !instrumentsLoadedOnce) {
        setError(errorMessage);
      }

      // Attempt retry for non-forced refresh if we haven't loaded instruments before
      if (!forceRefresh && !instrumentsLoadedOnce && retryAttempt < 2) {
        setInstrumentsRetryCount(retryAttempt + 1);
        setTimeout(() => {
          loadAvailableInstruments(false, retryAttempt + 1);
        }, 1000 * (retryAttempt + 1)); // Exponential backoff
      }
    } finally {
      setInstrumentsLoading(false);
    }
  }, [instrumentsLoadedOnce, setSnackbar]);

  // Handle manual symbol search when user types a symbol not in the list
  const handleSymbolInputChange = useCallback(async (event: any, value: string, reason: string) => {
    if (reason === 'input' && value && value.length >= 2) {
      const searchTerm = value.toUpperCase();
      // Check if the search term looks like a symbol and isn't already in our list
      if (/^[A-Z0-9.-]+$/.test(searchTerm) && !availableInstruments.some(inst => inst.symbol.includes(searchTerm))) {
        try {
          console.log(`Searching for symbol: ${searchTerm}`);
          const response = await InstrumentService.searchInstruments(searchTerm);
          if (response.success && response.data && response.data.length > 0) {
            // Add any new instruments found to our list
            setAvailableInstruments(prev => {
              const newInstruments = response.data.filter(newInst =>
                !prev.some(existingInst => existingInst.symbol === newInst.symbol)
              );
              if (newInstruments.length > 0) {
                console.log(`Found ${newInstruments.length} new instruments matching "${searchTerm}"`);
                return [...prev, ...newInstruments];
              }
              return prev;
            });
          }
        } catch (error) {
          console.error('Error searching for symbols:', error);
        }
      }
    }
  }, [availableInstruments]);

  // Load cached instruments on component mount
  useEffect(() => {
    const cachedInstruments = localStorage.getItem('positions_instruments_cache');
    if (cachedInstruments) {
      try {
        const parsed = JSON.parse(cachedInstruments);
        const cacheAge = Date.now() - parsed.timestamp;
        const cacheValid = parsed.data && parsed.timestamp && cacheAge < 5 * 60 * 1000; // 5 minutes cache

        if (cacheValid) {
          setAvailableInstruments(parsed.data);
          setInstrumentsLoadedOnce(true);
        } else {
          localStorage.removeItem('positions_instruments_cache');
        }
      } catch (error) {
        console.error('Error parsing cached instruments:', error);
        localStorage.removeItem('positions_instruments_cache');
      }
    }
  }, []);

  // Initial load - only load positions
  useEffect(() => {
    loadPositions();
  }, [loadPositions]);

  // Load instruments when add dialog opens
  useEffect(() => {
    if (addDialogOpen && !instrumentsLoadedOnce && !instrumentsLoading) {
      // Try to load from cache first
      const cachedInstruments = localStorage.getItem('positions_instruments_cache');
      if (cachedInstruments) {
        try {
          const parsed = JSON.parse(cachedInstruments);
          const cacheAge = Date.now() - parsed.timestamp;
          const cacheValid = parsed.data && parsed.timestamp && cacheAge < 5 * 60 * 1000; // 5 minutes cache

          if (cacheValid) {
            setAvailableInstruments(parsed.data);
            setInstrumentsLoadedOnce(true);
            return;
          }
        } catch (error) {
          console.error('Error parsing cached instruments for dialog:', error);
        }
      }

      // If no valid cache, load from API
      loadAvailableInstruments();
    }
  }, [addDialogOpen, instrumentsLoadedOnce, instrumentsLoading, loadAvailableInstruments]);

  // Reload when filters change
  useEffect(() => {
    loadPositions();
  }, [statusFilter, sideFilter, loadPositions]);

  // Utility functions for numeric input handling
  const parseNumericValue = (value: string | number | undefined): number | undefined => {
    if (value === undefined || value === null || value === '') {
      return undefined;
    }
    if (typeof value === 'number') {
      return value;
    }
    const parsed = parseFloat(value);
    return isNaN(parsed) ? undefined : parsed;
  };

  const formatInputValue = (value: string | number | undefined): string => {
    if (value === undefined || value === null) {
      return '';
    }
    return String(value);
  };

  // Convert form input data to API request format
  const convertFormDataToRequest = (inputData: EditFormInputData): EditFormData => {
    return {
      symbol: inputData.symbol,
      position: parseNumericValue(inputData.position) || 0,
      side: inputData.side,
      tradePrice: parseNumericValue(inputData.tradePrice) || 0,
      initPortfolioNetValue: parseNumericValue(inputData.initPortfolioNetValue),
      riskUnit: parseNumericValue(inputData.riskUnit),
      stopPercent: parseNumericValue(inputData.stopPercent),
      bbmbAdjPercent: parseNumericValue(inputData.bbmbAdjPercent),
      closePrice: parseNumericValue(inputData.closePrice),
      openDate: inputData.openDate || undefined,
      closeDate: inputData.closeDate || undefined,
    };
  };

  // Utility functions
  const formatCurrency = (value: number | undefined | null): string => {
    if (value === undefined || value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatNumber = (value: number | undefined | null, decimals = 2): string => {
    if (value === undefined || value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value);
  };

  const formatPercent = (value: number | undefined | null): string => {
    if (value === undefined || value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const getPnLColor = (value: number | undefined | null): string => {
    if (value === undefined || value === null) return theme.palette.text.secondary;
    if (value > 0) return theme.palette.success.main;
    if (value < 0) return theme.palette.error.main;
    return theme.palette.text.secondary;
  };

  const getPnLIcon = (value: number | undefined | null) => {
    if (value === undefined || value === null) return null;
    if (value > 0) return <TrendingUpIcon fontSize="small" />;
    if (value < 0) return <TrendingDownIcon fontSize="small" />;
    return null;
  };

  // Sorting
  const handleSort = (field: keyof Position) => {
    const newDirection = sortConfig.field === field && sortConfig.direction === 'desc' ? 'asc' : 'desc';
    setSortConfig({ field, direction: newDirection });
  };

  const sortedPositions = [...positions].sort((a, b) => {
    const aValue = a[sortConfig.field];
    const bValue = b[sortConfig.field];

    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortConfig.direction === 'desc' ? -comparison : comparison;
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      const comparison = aValue - bValue;
      return sortConfig.direction === 'desc' ? -comparison : comparison;
    }

    return 0;
  });

  // Pagination
  const paginatedPositions = sortedPositions.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Form validation - updated to handle input data with string values
  const validateForm = (inputData: EditFormInputData): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!inputData.symbol.trim()) {
      errors.symbol = 'Symbol is required';
    }

    const position = parseNumericValue(inputData.position);
    if (position === undefined || position <= 0) {
      errors.position = 'Position quantity is required and must be greater than 0';
    }

    const tradePrice = parseNumericValue(inputData.tradePrice);
    if (tradePrice === undefined || tradePrice <= 0) {
      errors.tradePrice = 'Trade price must be positive';
    }

    const stopPercent = parseNumericValue(inputData.stopPercent);
    if (stopPercent !== undefined && (stopPercent < 0 || stopPercent > 1)) {
      errors.stopPercent = 'Stop percent must be between 0 and 1';
    }

    const bbmbAdjPercent = parseNumericValue(inputData.bbmbAdjPercent);
    if (bbmbAdjPercent !== undefined && (bbmbAdjPercent < 0 || bbmbAdjPercent > 1)) {
      errors.bbmbAdjPercent = 'BBMB adjustment percent must be between 0 and 1';
    }

    const closePrice = parseNumericValue(inputData.closePrice);
    if (closePrice !== undefined && closePrice <= 0) {
      errors.closePrice = 'Close price must be positive';
    }

    // Date validation
    if (inputData.openDate && inputData.closeDate) {
      const openDate = new Date(inputData.openDate);
      const closeDate = new Date(inputData.closeDate);

      if (closeDate < openDate) {
        errors.closeDate = 'Close date cannot be before open date';
      }
    }

    return errors;
  };

  // Calculate P&L based on position status and appropriate price
  const calculatePnL = (closePrice: number, position: Position): CalculatedPnL => {
    if (!closePrice || !position.tradePrice || !position.position) {
      return { pnlValue: 0, pnlPercent: 0 };
    }

    const priceValue = position.position * closePrice;
    const tradeValue = position.position * position.tradePrice;

    let pnlValue: number;

    if (position.side === 'BUY') {
      // For BUY positions: current value - initial cost
      pnlValue = priceValue - tradeValue;
    } else {
      // For SELL positions: initial proceeds - current cost to buy back
      pnlValue = tradeValue - priceValue;
    }

    // Calculate P&L percentage
    let pnlPercent = 0;
    if (tradeValue !== 0) {
      if (position.side === 'BUY') {
        pnlPercent = pnlValue / tradeValue;
      } else {
        // For SELL positions, use absolute value of tradeValue for percentage calculation
        pnlPercent = pnlValue / Math.abs(tradeValue);
      }
    }

    return { pnlValue, pnlPercent };
  };

  // Get the appropriate price for P&L display based on position status
  const getDisplayPrice = (position: Position): number | null => {
    if (position.status === 'CLOSED') {
      // For CLOSED positions, use close_price if available
      return position.closePrice ?? null;
    } else {
      // For OPEN positions, use lastPrice (current market price) if available
      return position.lastPrice ?? null;
    }
  };

  // Get P&L values for display based on position status
  const getDisplayPnL = (position: Position): { pnlValue: number | null, pnlPercent: number | null } => {
    if (position.status === 'CLOSED') {
      // For CLOSED positions, use the stored P&L values (calculated from close_price)
      return {
        pnlValue: position.pnlValue ?? null,
        pnlPercent: position.pnlPercent ?? null
      };
    } else {
      // For OPEN positions, use the stored P&L values (calculated from latest market price)
      return {
        pnlValue: position.pnlValue ?? null,
        pnlPercent: position.pnlPercent ?? null
      };
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      symbol: '',
      position: '',
      side: 'BUY',
      tradePrice: '',
      initPortfolioNetValue: '',
      riskUnit: '',
      stopPercent: '',
      bbmbAdjPercent: '',
      closePrice: '',
      openDate: '',
      closeDate: '',
      aggressiveStopPercent: '',
      conservativeStopPercent: '',
    });
    setFormErrors({});
    setCalculatedPnL(null);
  };

  // Handle form field changes - updated to support string values during input
  const handleFormChange = (field: keyof EditFormInputData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // Calculate P&L in real-time when close price or quantity changes (only in edit mode)
    if ((field === 'closePrice' || field === 'position') && selectedPosition) {
      const currentClosePrice = parseNumericValue(field === 'closePrice' ? value : formData.closePrice);
      const currentQuantity = parseNumericValue(field === 'position' ? value : formData.position);

      if (currentClosePrice && currentClosePrice > 0 && currentQuantity && currentQuantity > 0) {
        // Create a temporary position object with updated values for P&L calculation
        const tempPosition = {
          ...selectedPosition,
          position: currentQuantity,
        };
        const pnl = calculatePnL(currentClosePrice, tempPosition);
        setCalculatedPnL(pnl);
      } else {
        setCalculatedPnL(null);
      }
    }
  };
  // CRUD Operations
  const handleAddPosition = async () => {
    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setSubmitting(true);

      const convertedData = convertFormDataToRequest(formData);
      const request: CreatePositionRequest = {
        ...convertedData,
        symbol: formData.symbol.toUpperCase(),
      };

      const response = await PositionService.createPosition(request);

      if (response.success) {
        await loadPositions();
        setAddDialogOpen(false);
        resetForm();
        setSnackbar({
          open: true,
          message: `Successfully created position for ${formData.symbol.toUpperCase()}`,
          severity: 'success',
        });
      } else {
        setError(response.message || 'Failed to create position');
      }
    } catch (err: any) {
      console.error('Error creating position:', err);
      if (err.message?.includes('Symbol not found')) {
        setFormErrors({ symbol: 'Symbol not found in instruments' });
      } else {
        setError('Failed to create position: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditPosition = async () => {
    if (!selectedPosition) return;

    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    const convertedData = convertFormDataToRequest(formData);

    // Check if quantity has changed
    const quantityChanged = convertedData.position !== selectedPosition.position;

    if (quantityChanged) {
      setSnackbar({
        open: true,
        message: 'Warning: Quantity changes are not yet supported by the backend. Only other fields will be updated.',
        severity: 'warning',
      });
    }

    try {
      setSubmitting(true);

      const request: UpdatePositionRequest = {
        // Note: Not including position/quantity as it's not supported in UpdatePositionRequest yet
        // When backend support is added, include: position: convertedData.position,
        riskUnit: convertedData.riskUnit,
        stopPercent: convertedData.stopPercent,
        bbmbAdjPercent: convertedData.bbmbAdjPercent,
        closePrice: convertedData.closePrice,
        openDate: convertedData.openDate,
        closeDate: convertedData.closeDate,
      };

      // If tradePrice changed, we need to handle it separately since it's the entry price
      // For now, we'll skip updating tradePrice as it should be immutable after position creation
      // In the future, this might need a separate endpoint for correcting entry prices

      const response = await PositionService.updatePosition(selectedPosition.id, request);

      if (response.success) {
        await loadPositions();
        setEditDialogOpen(false);
        setSelectedPosition(null);
        resetForm();

        let message = `Successfully updated position for ${selectedPosition.symbol}`;
        if (quantityChanged) {
          message += ' (Note: Quantity changes require backend support and were not applied)';
        }

        setSnackbar({
          open: true,
          message: message,
          severity: quantityChanged ? 'warning' : 'success',
        });
      } else {
        setError(response.message || 'Failed to update position');
      }
    } catch (err: any) {
      console.error('Error updating position:', err);
      setError('Failed to update position: ' + (err.message || 'Unknown error'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeletePosition = async () => {
    if (!selectedPosition) return;

    try {
      setSubmitting(true);

      const response = await PositionService.deletePosition(selectedPosition.id);

      if (response.success) {
        await loadPositions();
        setDeleteDialogOpen(false);
        setSelectedPosition(null);
        setSnackbar({
          open: true,
          message: `Successfully deleted position for ${selectedPosition.symbol}`,
          severity: 'success',
        });
      } else {
        setError(response.message || 'Failed to delete position');
      }
    } catch (err: any) {
      console.error('Error deleting position:', err);
      setError('Failed to delete position: ' + (err.message || 'Unknown error'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleClosePosition = async (position: Position) => {
    try {
      const response = await PositionService.closePosition(position.id);

      if (response.success) {
        await loadPositions();
        setSnackbar({
          open: true,
          message: `Successfully closed position for ${position.symbol}`,
          severity: 'success',
        });
      } else {
        setError(response.message || 'Failed to close position');
      }
    } catch (err: any) {
      console.error('Error closing position:', err);
      setError('Failed to close position: ' + (err.message || 'Unknown error'));
    }
  };

  // Dialog handlers
  const handleOpenAddDialog = () => {
    resetForm();
    setAddDialogOpen(true);
  };

  const handleOpenEditDialog = (position: Position) => {
    setSelectedPosition(position);
    setFormData({
      symbol: position.symbol,
      position: formatInputValue(position.position),
      side: position.side,
      tradePrice: formatInputValue(position.tradePrice),
      initPortfolioNetValue: formatInputValue(position.initPortfolioNetValue),
      riskUnit: formatInputValue(position.riskUnit),
      stopPercent: formatInputValue(position.stopPercent),
      bbmbAdjPercent: formatInputValue(position.bbmbAdjPercent),
      closePrice: formatInputValue(position.closePrice),
      openDate: position.openDate || (position.createdDate ? position.createdDate.split('T')[0] : ''),
      closeDate: position.closeDate || '',
    });

    // Calculate initial P&L if close price exists
    if (position.closePrice && position.closePrice > 0) {
      const pnl = calculatePnL(position.closePrice, position);
      setCalculatedPnL(pnl);
    } else {
      setCalculatedPnL(null);
    }

    setEditDialogOpen(true);
  };

  const handleOpenDeleteDialog = (position: Position) => {
    setSelectedPosition(position);
    setDeleteDialogOpen(true);
  };

  const handleClonePosition = (position: Position) => {
    // Pre-populate the Add Position dialog with data from the selected position
    setFormData({
      symbol: position.symbol,
      position: formatInputValue(position.position),
      side: position.side,
      tradePrice: formatInputValue(position.tradePrice),
      initPortfolioNetValue: formatInputValue(position.initPortfolioNetValue),
      riskUnit: formatInputValue(position.riskUnit),
      stopPercent: formatInputValue(position.stopPercent),
      bbmbAdjPercent: formatInputValue(position.bbmbAdjPercent),
      closePrice: '', // Clear close price for new position
      openDate: '', // Clear dates for new position
      closeDate: '',
    });
    setFormErrors({});
    setCalculatedPnL(null);
    setAddDialogOpen(true);

    // Show a snackbar to indicate this is a cloned position
    setSnackbar({
      open: true,
      message: `Cloning position for ${position.symbol}. Modify as needed and create.`,
      severity: 'info',
    });
  };

  const handleCloseDialogs = () => {
    setAddDialogOpen(false);
    setEditDialogOpen(false);
    setDeleteDialogOpen(false);
    setSelectedPosition(null);
    resetForm();
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Handle OHLCV data update
  const handleUpdateOHLCVData = () => {
    if (positions.length === 0) {
      setSnackbar({
        open: true,
        message: 'No positions found to update OHLCV data',
        severity: 'warning',
      });
      return;
    }
    setShowUpdateConfirmation(true);
  };

  const confirmUpdateOHLCVData = async () => {
    try {
      setShowUpdateConfirmation(false);
      setUpdatingOHLCV(true);
      setShowUpdateDialog(true);
      setUpdateStartTime(new Date());
      setUpdateResult(null);

      const response = await PositionService.updateOHLCVDataForPositions({
        recalculatePnL: true,
        dryRun: false,
        stopLossMode: stopLossMode
      });

      if (response.success && response.data) {
        setUpdateResult(response.data);

        // Show success message
        const message = response.data.summary || 'OHLCV data update completed successfully';
        setSnackbar({
          open: true,
          message: message,
          severity: response.data.ohlcvErrorCount === 0 ? 'success' : 'warning',
        });

        // Reload positions to show updated P&L
        await loadPositions();
      } else {
        setSnackbar({
          open: true,
          message: response.message || 'Failed to update OHLCV data',
          severity: 'error',
        });
        setUpdateResult({
          totalSymbols: 0,
          ohlcvSuccessCount: 0,
          ohlcvErrorCount: 1,
          totalRecordsUpdated: 0,
          pnlUpdatedCount: 0,
          totalPositions: positions.length,
          processingTimeMs: 0,
          ohlcvResults: [],
          summary: response.message || 'Update failed'
        });
      }
    } catch (err: any) {
      console.error('Error updating OHLCV data:', err);
      setSnackbar({
        open: true,
        message: err.message || 'Failed to update OHLCV data',
        severity: 'error',
      });
      setUpdateResult({
        totalSymbols: 0,
        ohlcvSuccessCount: 0,
        ohlcvErrorCount: 1,
        totalRecordsUpdated: 0,
        pnlUpdatedCount: 0,
        totalPositions: positions.length,
        processingTimeMs: 0,
        ohlcvResults: [],
        summary: err.message || 'Update failed due to network error'
      });
    } finally {
      setUpdatingOHLCV(false);
    }
  };

  // Handle P&L recalculation
  const handleRecalculatePnL = () => {
    if (positions.length === 0) {
      setSnackbar({
        open: true,
        message: 'No positions found to recalculate P&L',
        severity: 'warning',
      });
      return;
    }
    setShowPnLConfirmation(true);
  };

  const confirmRecalculatePnL = async () => {
    try {
      setShowPnLConfirmation(false);
      setRecalculatingPnL(true);

      const response = await PositionService.recalculatePnLForAllPositions({
        stopLossMode: stopLossMode
      });

      if (response.success && response.data) {
        setSnackbar({
          open: true,
          message: response.data.message || 'P&L recalculation completed successfully',
          severity: 'success',
        });

        // Reload positions to show updated P&L
        await loadPositions();
      } else {
        setSnackbar({
          open: true,
          message: response.message || 'Failed to recalculate P&L',
          severity: 'error',
        });
      }
    } catch (err: any) {
      console.error('Error recalculating P&L:', err);
      setSnackbar({
        open: true,
        message: err.message || 'Failed to recalculate P&L',
        severity: 'error',
      });
    } finally {
      setRecalculatingPnL(false);
    }
  };

  const handleCloseUpdateDialog = () => {
    setShowUpdateDialog(false);
    setUpdateResult(null);
    setUpdateStartTime(null);
  };
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Portfolio Positions
      </Typography>

      {/* Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                >
                  <MenuItem value="ALL">All Positions</MenuItem>
                  <MenuItem value="OPEN">Open Positions</MenuItem>
                  <MenuItem value="CLOSED">Closed Positions</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Side</InputLabel>
                <Select
                  value={sideFilter}
                  label="Side"
                  onChange={(e) => setSideFilter(e.target.value as any)}
                >
                  <MenuItem value="ALL">All Sides</MenuItem>
                  <MenuItem value="BUY">Long Positions</MenuItem>
                  <MenuItem value="SELL">Short Positions</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadPositions}
                disabled={loading}
                fullWidth
              >
                Refresh
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleOpenAddDialog}
                fullWidth
              >
                Add Position
              </Button>
            </Grid>
          </Grid>

          {/* Stop-Loss Mode Selection */}
          <Divider sx={{ my: 2 }} />
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12}>
              <StopLossModeSelector
                value={stopLossMode}
                onChange={setStopLossMode}
                disabled={loading || updatingOHLCV || recalculatingPnL}
                variant="compact"
                showDescription={false}
              />
            </Grid>
          </Grid>

          {/* OHLCV Update Controls */}
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="outlined"
                startIcon={<UpdateIcon />}
                onClick={handleUpdateOHLCVData}
                disabled={loading || updatingOHLCV || recalculatingPnL || positions.length === 0}
                color="secondary"
                fullWidth
              >
                {updatingOHLCV ? 'Updating...' : 'Update OHLCV Data'}
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                variant="outlined"
                startIcon={<CalculateIcon />}
                onClick={handleRecalculatePnL}
                disabled={loading || updatingOHLCV || recalculatingPnL || positions.length === 0}
                color="primary"
                fullWidth
              >
                {recalculatingPnL ? 'Calculating...' : 'Recalculate P&L'}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Positions Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.field === 'symbol'}
                    direction={sortConfig.field === 'symbol' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('symbol')}
                  >
                    Symbol
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={sortConfig.field === 'position'}
                    direction={sortConfig.field === 'position' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('position')}
                  >
                    Quantity
                  </TableSortLabel>
                </TableCell>
                <TableCell align="center">Side</TableCell>
                <TableCell align="center">Status</TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={sortConfig.field === 'tradePrice'}
                    direction={sortConfig.field === 'tradePrice' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('tradePrice')}
                  >
                    Entry Price
                  </TableSortLabel>
                </TableCell>
                {!isMobile && (
                  <>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortConfig.field === 'closePrice'}
                        direction={sortConfig.field === 'closePrice' ? sortConfig.direction : 'asc'}
                        onClick={() => handleSort('closePrice')}
                      >
                        Close Price
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">Current Price</TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortConfig.field === 'pnlValue'}
                        direction={sortConfig.field === 'pnlValue' ? sortConfig.direction : 'asc'}
                        onClick={() => handleSort('pnlValue')}
                      >
                        P&L Value
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortConfig.field === 'pnlPercent'}
                        direction={sortConfig.field === 'pnlPercent' ? sortConfig.direction : 'asc'}
                        onClick={() => handleSort('pnlPercent')}
                      >
                        P&L %
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">Stop Loss</TableCell>
                  </>
                )}
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={isMobile ? 6 : 10} align="center">
                    <Box py={4}>
                      <CircularProgress />
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                        Loading positions...
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : paginatedPositions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isMobile ? 6 : 11} align="center">
                    <Box py={4}>
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        No positions found
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {statusFilter !== 'ALL' || sideFilter !== 'ALL'
                          ? 'No positions match the current filters.'
                          : 'Create your first position to start tracking your portfolio.'}
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleOpenAddDialog}
                      >
                        Add Position
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedPositions.map((position) => (
                  <TableRow key={position.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {position.symbol}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      {formatNumber(position.position, 6)}
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={position.side}
                        color={position.side === 'BUY' ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={position.status}
                        color={position.status === 'OPEN' ? 'primary' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(position.tradePrice)}
                    </TableCell>
                    {!isMobile && (
                      <>
                        <TableCell align="right">
                          {position.closePrice ? formatCurrency(position.closePrice) : 'N/A'}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(position.lastPrice)}
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" alignItems="center" justifyContent="flex-end" gap={0.5}>
                            {getPnLIcon(position.pnlValue)}
                            <Typography
                              variant="body2"
                              sx={{ color: getPnLColor(position.pnlValue), fontWeight: 600 }}
                            >
                              {formatCurrency(position.pnlValue)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography
                            variant="body2"
                            sx={{ color: getPnLColor(position.pnlValue), fontWeight: 600 }}
                          >
                            {formatPercent(position.pnlPercent)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(position.effectiveStopValue)}
                        </TableCell>
                      </>
                    )}
                    <TableCell align="center">
                      <Box display="flex" gap={0.5} justifyContent="center">
                        <Tooltip title="Edit Position">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenEditDialog(position)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Clone Position">
                          <IconButton
                            size="small"
                            onClick={() => handleClonePosition(position)}
                            color="primary"
                          >
                            <CloneIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        {position.status === 'OPEN' && (
                          <Tooltip title="Close Position">
                            <IconButton
                              size="small"
                              onClick={() => handleClosePosition(position)}
                              color="warning"
                            >
                              <CloseIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Delete Position">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDeleteDialog(position)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {!loading && positions.length > 0 && (
          <TablePagination
            rowsPerPageOptions={[25, 50, 100]}
            component="div"
            count={sortedPositions.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        )}
      </Paper>
      {/* Add Position Dialog */}
      <Dialog open={addDialogOpen} onClose={handleCloseDialogs} maxWidth="md" fullWidth>
        <DialogTitle>Add New Position</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <Box>
                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Typography variant="subtitle2">
                    Symbol Selection
                  </Typography>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => loadAvailableInstruments(true)}
                    disabled={instrumentsLoading}
                    startIcon={instrumentsLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
                  >
                    {instrumentsRetryCount > 0 ? `Retry ${instrumentsRetryCount}/2` : 'Refresh'}
                  </Button>
                  {!instrumentsLoadedOnce && instrumentsRetryCount > 0 && (
                    <Chip
                      size="small"
                      label={`Retrying... (${instrumentsRetryCount}/2)`}
                      color="warning"
                      variant="outlined"
                    />
                  )}
                </Box>
                <Autocomplete
                  options={availableInstruments.map(instrument => ({
                    label: `${instrument.symbol} - ${instrument.name}`,
                    value: instrument.symbol,
                    instrument: instrument
                  }))}
                  getOptionLabel={(option) => typeof option === 'string' ? option : option.label}
                  value={availableInstruments.map(instrument => ({
                    label: `${instrument.symbol} - ${instrument.name}`,
                    value: instrument.symbol,
                    instrument: instrument
                  })).find(option => option.value === formData.symbol) || null}
                  onChange={(_, newValue) => {
                    const symbolValue = typeof newValue === 'string' ? newValue : newValue?.value || '';
                    handleFormChange('symbol', symbolValue);
                  }}
                  onInputChange={handleSymbolInputChange}
                  loading={instrumentsLoading}
                  freeSolo={!instrumentsLoadedOnce || availableInstruments.length === 0}
                  filterOptions={(options, { inputValue }) => {
                    // Enhanced filtering to show partial matches
                    const filtered = options.filter(option =>
                      option.label.toLowerCase().includes(inputValue.toLowerCase()) ||
                      option.value.toLowerCase().includes(inputValue.toLowerCase())
                    );
                    return filtered;
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Symbol"
                      required
                      placeholder={instrumentsLoadedOnce ? "Search for a symbol..." : "Enter symbol manually (e.g., AAPL)"}
                      error={!!formErrors.symbol}
                      helperText={
                        formErrors.symbol ||
                        (instrumentsLoadedOnce
                          ? `${availableInstruments.length} instruments available`
                          : instrumentsLoading
                            ? "Loading instruments..."
                            : "Enter symbol manually or refresh to load instruments"
                        )
                      }
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {instrumentsLoading ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                      onChange={(e) => {
                        // Handle manual input when freeSolo is enabled
                        if (!instrumentsLoadedOnce || availableInstruments.length === 0) {
                          handleFormChange('symbol', e.target.value.toUpperCase());
                        }
                      }}
                    />
                  )}
                  renderOption={(props, option) => {
                    if (typeof option === 'string') {
                      return (
                        <Box component="li" {...props}>
                          <Typography variant="body2" fontWeight="bold">
                            {option}
                          </Typography>
                        </Box>
                      );
                    }
                    return (
                      <Box component="li" {...props}>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {option.value}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {option.instrument.name}
                          </Typography>
                        </Box>
                      </Box>
                    );
                  }}
                  disabled={instrumentsLoading}
                  fullWidth
                />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Side</InputLabel>
                <Select
                  value={formData.side}
                  label="Side"
                  onChange={(e) => handleFormChange('side', e.target.value)}
                >
                  <MenuItem value="BUY">Long (BUY)</MenuItem>
                  <MenuItem value="SELL">Short (SELL)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Quantity"
                type="number"
                required
                value={formData.position}
                onChange={(e) => handleFormChange('position', e.target.value)}
                error={!!formErrors.position}
                helperText={formErrors.position || 'Number of shares/units'}
                inputProps={{ step: 0.000001, min: 0 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Entry Price"
                type="number"
                required
                value={formData.tradePrice}
                onChange={(e) => handleFormChange('tradePrice', e.target.value)}
                error={!!formErrors.tradePrice}
                helperText={formErrors.tradePrice || 'Price per share/unit'}
                inputProps={{ step: 0.01, min: 0.01 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Initial Portfolio Value"
                type="number"
                value={formData.initPortfolioNetValue}
                onChange={(e) => handleFormChange('initPortfolioNetValue', e.target.value)}
                helperText="Total portfolio value when position was opened"
                inputProps={{ step: 0.01, min: 0 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Risk Unit"
                type="number"
                value={formData.riskUnit}
                onChange={(e) => handleFormChange('riskUnit', e.target.value)}
                helperText="Risk unit size for position sizing"
                inputProps={{ step: 0.01, min: 0 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Stop Loss %"
                type="number"
                value={formData.stopPercent}
                onChange={(e) => handleFormChange('stopPercent', e.target.value)}
                error={!!formErrors.stopPercent}
                helperText={formErrors.stopPercent || 'Stop loss percentage (e.g., 0.02 for 2%)'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="BBMB Adjustment %"
                type="number"
                value={formData.bbmbAdjPercent}
                onChange={(e) => handleFormChange('bbmbAdjPercent', e.target.value)}
                error={!!formErrors.bbmbAdjPercent}
                helperText={formErrors.bbmbAdjPercent || 'Bollinger Band Middle Band adjustment %'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>

            {/* Enhanced Risk Management Parameters */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Enhanced Risk Management (Optional)
                </Typography>
              </Divider>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Aggressive Stop %"
                type="number"
                value={formData.aggressiveStopPercent}
                onChange={(e) => handleFormChange('aggressiveStopPercent', e.target.value)}
                error={!!formErrors.aggressiveStopPercent}
                helperText={formErrors.aggressiveStopPercent || 'Aggressive stop percentage for enhanced mode (e.g., 0.05 for 5%)'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Conservative Stop %"
                type="number"
                value={formData.conservativeStopPercent}
                onChange={(e) => handleFormChange('conservativeStopPercent', e.target.value)}
                error={!!formErrors.conservativeStopPercent}
                helperText={formErrors.conservativeStopPercent || 'Conservative stop percentage for enhanced mode (e.g., 0.03 for 3%)'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Open Date (Optional)"
                type="date"
                value={formData.openDate}
                onChange={(e) => handleFormChange('openDate', e.target.value)}
                error={!!formErrors.openDate}
                helperText={formErrors.openDate || 'Date when position was opened (defaults to today if not set)'}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialogs} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleAddPosition}
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={16} /> : <AddIcon />}
          >
            {submitting ? 'Creating...' : 'Create Position'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Position Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseDialogs} maxWidth="md" fullWidth>
        <DialogTitle>Edit Position - {selectedPosition?.symbol}</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Field Editability:</strong>
            </Typography>
            <Typography variant="body2" component="div">
              • <strong>Quantity & Entry Price:</strong> Editable for data corrections<br/>
              • <strong>Current Market Price:</strong> Read-only (from OHLCV data)<br/>
              • <strong>Close Price:</strong> Editable for position closure<br/>
              • <strong>Note:</strong> Quantity changes require backend support (coming soon)
            </Typography>
          </Alert>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Symbol"
                value={formData.symbol}
                disabled
                helperText="Symbol cannot be changed"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Side"
                value={formData.side}
                disabled
                helperText="Side cannot be changed"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Quantity"
                type="number"
                value={formData.position}
                onChange={(e) => handleFormChange('position', e.target.value)}
                error={!!formErrors.position}
                helperText={formErrors.position || 'Number of shares/units (editable for corrections)'}
                inputProps={{ step: 0.000001, min: 0.000001 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Entry Price"
                type="number"
                value={formData.tradePrice}
                onChange={(e) => handleFormChange('tradePrice', e.target.value)}
                error={!!formErrors.tradePrice}
                helperText={formErrors.tradePrice || 'Original trade execution price'}
                inputProps={{ step: 0.01, min: 0.01 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Current Market Price"
                value={selectedPosition?.lastPrice ? formatCurrency(selectedPosition.lastPrice) : 'N/A'}
                disabled
                helperText="Latest market price from OHLCV data (read-only)"
                InputProps={{
                  startAdornment: selectedPosition?.lastPrice ? undefined : (
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      No data
                    </Typography>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Risk Unit"
                type="number"
                value={formData.riskUnit}
                onChange={(e) => handleFormChange('riskUnit', e.target.value)}
                helperText="Risk unit size for position sizing"
                inputProps={{ step: 0.01, min: 0 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Stop Loss %"
                type="number"
                value={formData.stopPercent}
                onChange={(e) => handleFormChange('stopPercent', e.target.value)}
                error={!!formErrors.stopPercent}
                helperText={formErrors.stopPercent || 'Stop loss percentage (e.g., 0.02 for 2%)'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="BBMB Adjustment %"
                type="number"
                value={formData.bbmbAdjPercent}
                onChange={(e) => handleFormChange('bbmbAdjPercent', e.target.value)}
                error={!!formErrors.bbmbAdjPercent}
                helperText={formErrors.bbmbAdjPercent || 'Bollinger Band Middle Band adjustment %'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>

            {/* Enhanced Risk Management Parameters */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Enhanced Risk Management (Optional)
                </Typography>
              </Divider>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Aggressive Stop %"
                type="number"
                value={formData.aggressiveStopPercent}
                onChange={(e) => handleFormChange('aggressiveStopPercent', e.target.value)}
                error={!!formErrors.aggressiveStopPercent}
                helperText={formErrors.aggressiveStopPercent || 'Aggressive stop percentage for enhanced mode (e.g., 0.05 for 5%)'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Conservative Stop %"
                type="number"
                value={formData.conservativeStopPercent}
                onChange={(e) => handleFormChange('conservativeStopPercent', e.target.value)}
                error={!!formErrors.conservativeStopPercent}
                helperText={formErrors.conservativeStopPercent || 'Conservative stop percentage for enhanced mode (e.g., 0.03 for 3%)'}
                inputProps={{ step: 0.001, min: 0, max: 1 }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Close Price"
                type="number"
                value={formData.closePrice}
                onChange={(e) => handleFormChange('closePrice', e.target.value)}
                error={!!formErrors.closePrice}
                helperText={formErrors.closePrice || 'Price at which position was closed (for closed positions)'}
                inputProps={{ step: 0.01, min: 0.01 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Open Date"
                type="date"
                value={formData.openDate}
                onChange={(e) => handleFormChange('openDate', e.target.value)}
                error={!!formErrors.openDate}
                helperText={formErrors.openDate || 'Date when position was opened'}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Close Date (Optional)"
                type="date"
                value={formData.closeDate}
                onChange={(e) => handleFormChange('closeDate', e.target.value)}
                error={!!formErrors.closeDate}
                helperText={formErrors.closeDate || 'Date when position was closed (optional)'}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            {/* Real-time P&L Calculation Display */}
            {calculatedPnL && formData.closePrice && formData.closePrice > 0 && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Calculated P&L (Real-time)
                    </Typography>
                  </Divider>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.300' }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      P&L Value
                    </Typography>
                    <Box display="flex" alignItems="center" gap={0.5}>
                      {getPnLIcon(calculatedPnL.pnlValue)}
                      <Typography
                        variant="h6"
                        sx={{ color: getPnLColor(calculatedPnL.pnlValue), fontWeight: 600 }}
                      >
                        {formatCurrency(calculatedPnL.pnlValue)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.300' }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      P&L Percentage
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{ color: getPnLColor(calculatedPnL.pnlValue), fontWeight: 600 }}
                    >
                      {formatPercent(calculatedPnL.pnlPercent)}
                    </Typography>
                  </Box>
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialogs} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleEditPosition}
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={16} /> : <EditIcon />}
          >
            {submitting ? 'Updating...' : 'Update Position'}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDialogs} maxWidth="sm" fullWidth>
        <DialogTitle>Delete Position</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to delete this position?
          </Typography>
          {selectedPosition && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="body2">
                <strong>Symbol:</strong> {selectedPosition.symbol}
              </Typography>
              <Typography variant="body2">
                <strong>Quantity:</strong> {formatNumber(selectedPosition.position, 6)}
              </Typography>
              <Typography variant="body2">
                <strong>Side:</strong> {selectedPosition.side}
              </Typography>
              <Typography variant="body2">
                <strong>Entry Price:</strong> {formatCurrency(selectedPosition.tradePrice)}
              </Typography>
              <Typography variant="body2">
                <strong>Status:</strong> {selectedPosition.status}
              </Typography>
            </Box>
          )}
          <Alert severity="warning" sx={{ mt: 2 }}>
            This action cannot be undone. The position will be permanently removed from your portfolio.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialogs} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleDeletePosition}
            variant="contained"
            color="error"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={16} /> : <DeleteIcon />}
          >
            {submitting ? 'Deleting...' : 'Delete Position'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* OHLCV Update Confirmation Dialog */}
      <Dialog open={showUpdateConfirmation} onClose={() => setShowUpdateConfirmation(false)}>
        <DialogTitle>Update OHLCV Data</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            This will update OHLCV historical data for all symbols in your positions and recalculate P&L using the latest market prices.
          </Alert>
          <Typography variant="body2" paragraph>
            The operation will:
          </Typography>
          <Box component="ul" sx={{ pl: 2, mb: 2 }}>
            <Typography component="li" variant="body2">
              Download latest OHLCV data from Yahoo Finance for {new Set(positions.map(p => p.symbol)).size} unique symbols
            </Typography>
            <Typography component="li" variant="body2">
              Recalculate P&L for {positions.length} positions using updated market prices with <strong>{stopLossMode}</strong> stop-loss mode
            </Typography>
            <Typography component="li" variant="body2">
              This may take several minutes depending on the number of symbols
            </Typography>
          </Box>
          <Alert severity="warning">
            This operation downloads data from external APIs and may take significant time. Please be patient.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUpdateConfirmation(false)}>
            Cancel
          </Button>
          <Button
            onClick={confirmUpdateOHLCVData}
            variant="contained"
            color="secondary"
            startIcon={<UpdateIcon />}
          >
            Update OHLCV Data
          </Button>
        </DialogActions>
      </Dialog>

      {/* P&L Recalculation Confirmation Dialog */}
      <Dialog open={showPnLConfirmation} onClose={() => setShowPnLConfirmation(false)}>
        <DialogTitle>Recalculate P&L</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            This will recalculate P&L metrics for all positions using the latest market prices from OHLCV data.
          </Alert>
          <Typography variant="body2" paragraph>
            The operation will update P&L values and percentages for {positions.length} positions using <strong>{stopLossMode}</strong> stop-loss calculation mode.
          </Typography>
          <Alert severity="warning">
            Make sure OHLCV data is up-to-date before recalculating P&L for accurate results.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPnLConfirmation(false)}>
            Cancel
          </Button>
          <Button
            onClick={confirmRecalculatePnL}
            variant="contained"
            color="primary"
            startIcon={<CalculateIcon />}
          >
            Recalculate P&L
          </Button>
        </DialogActions>
      </Dialog>

      {/* OHLCV Update Progress Dialog */}
      <Dialog
        open={showUpdateDialog}
        onClose={updatingOHLCV ? undefined : handleCloseUpdateDialog}
        maxWidth="md"
        fullWidth
        disableEscapeKeyDown={updatingOHLCV}
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <UpdateIcon />
            OHLCV Data Update Progress
            {updatingOHLCV && <CircularProgress size={20} />}
          </Box>
        </DialogTitle>
        <DialogContent>
          {updatingOHLCV ? (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                Updating OHLCV data and recalculating P&L... Please wait.
              </Alert>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <CircularProgress size={24} />
                <Typography variant="body2">
                  Processing {new Set(positions.map(p => p.symbol)).size} symbols from {positions.length} positions...
                </Typography>
              </Box>
              {updateStartTime && (
                <Typography variant="body2" color="text.secondary">
                  Elapsed time: {Math.floor((Date.now() - updateStartTime.getTime()) / 1000)} seconds
                </Typography>
              )}
            </Box>
          ) : updateResult ? (
            <Box>
              <Alert
                severity={updateResult.ohlcvErrorCount === 0 ? 'success' : 'warning'}
                sx={{ mb: 2 }}
              >
                {updateResult.summary}
              </Alert>

              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="primary">
                      {updateResult.totalSymbols}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Symbols
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="success.main">
                      {updateResult.ohlcvSuccessCount}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      OHLCV Success
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="info.main">
                      {updateResult.totalRecordsUpdated}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Records Updated
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="secondary.main">
                      {updateResult.pnlUpdatedCount}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      P&L Updated
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Processing time: {(updateResult.processingTimeMs / 1000).toFixed(1)} seconds
              </Typography>

              {updateResult.ohlcvErrorCount > 0 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  {updateResult.ohlcvErrorCount} symbols had errors during OHLCV update.
                  Check the detailed results below.
                </Alert>
              )}
            </Box>
          ) : null}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseUpdateDialog}
            disabled={updatingOHLCV}
            variant={updateResult ? 'contained' : 'outlined'}
          >
            {updatingOHLCV ? 'Please Wait...' : 'Close'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Positions;
